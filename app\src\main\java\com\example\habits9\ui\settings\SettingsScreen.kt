package com.example.habits9.ui.settings

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.RadioButton
import androidx.compose.material3.RadioButtonDefaults
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel

// Design System Colors - Dark Theme (from style guide)
private val DarkBackground = Color(0xFF121826) // background
private val SurfaceVariantDark = Color(0xFF1A202C) // surface-variant
private val TextPrimary = Color(0xFFE2E8F0) // text-primary
private val TextSecondary = Color(0xFFA0AEC0) // text-secondary
private val AccentPrimary = Color(0xFF81E6D9) // accent-primary

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsScreen(
    onBackClick: () -> Unit = {},
    settingsViewModel: SettingsViewModel = hiltViewModel()
) {
    val firstDayOfWeek by settingsViewModel.firstDayOfWeek.collectAsState()
    
    Scaffold(
        containerColor = DarkBackground,
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "Settings",
                        color = TextPrimary,
                        fontSize = 18.sp,
                        fontFamily = FontFamily.Default,
                        fontWeight = FontWeight.Medium
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onBackClick) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "Back",
                            tint = TextPrimary
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = SurfaceVariantDark,
                    titleContentColor = TextPrimary,
                    navigationIconContentColor = TextPrimary
                )
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(horizontal = 20.dp) // viewport spacing from style guide
        ) {
            Spacer(modifier = Modifier.height(16.dp)) // section spacing
            
            // First day of week setting section
            Text(
                text = "First day of week",
                color = TextPrimary,
                fontSize = 16.sp,
                fontFamily = FontFamily.Default,
                fontWeight = FontWeight.Medium,
                modifier = Modifier.padding(bottom = 12.dp)
            )
            
            // Radio button group
            Column(
                modifier = Modifier.selectableGroup()
            ) {
                // Sunday option
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .selectable(
                            selected = (firstDayOfWeek == "SUNDAY"),
                            onClick = { settingsViewModel.updateFirstDayOfWeek("SUNDAY") },
                            role = Role.RadioButton
                        )
                        .padding(vertical = 8.dp), // compact spacing
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = (firstDayOfWeek == "SUNDAY"),
                        onClick = null, // handled by Row's selectable
                        colors = RadioButtonDefaults.colors(
                            selectedColor = AccentPrimary,
                            unselectedColor = TextSecondary
                        )
                    )
                    Spacer(modifier = Modifier.width(12.dp))
                    Text(
                        text = "Sunday",
                        color = TextPrimary,
                        fontSize = 14.sp,
                        fontFamily = FontFamily.Default,
                        fontWeight = FontWeight.Normal
                    )
                }
                
                // Monday option
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .selectable(
                            selected = (firstDayOfWeek == "MONDAY"),
                            onClick = { settingsViewModel.updateFirstDayOfWeek("MONDAY") },
                            role = Role.RadioButton
                        )
                        .padding(vertical = 8.dp), // compact spacing
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = (firstDayOfWeek == "MONDAY"),
                        onClick = null, // handled by Row's selectable
                        colors = RadioButtonDefaults.colors(
                            selectedColor = AccentPrimary,
                            unselectedColor = TextSecondary
                        )
                    )
                    Spacer(modifier = Modifier.width(12.dp))
                    Text(
                        text = "Monday",
                        color = TextPrimary,
                        fontSize = 14.sp,
                        fontFamily = FontFamily.Default,
                        fontWeight = FontWeight.Normal
                    )
                }
            }
        }
    }
}