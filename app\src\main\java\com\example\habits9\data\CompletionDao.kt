package com.example.habits9.data

import androidx.room.*
import kotlinx.coroutines.flow.Flow

/**
 * Data Access Object for habit completions.
 * Provides methods to insert, update, delete, and query habit completion records.
 */
@Dao
interface CompletionDao {
    
    /**
     * Inserts a new completion record. If a completion with the same ID exists, it will be replaced.
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertCompletion(completion: Completion)

    /**
     * Deletes a completion record from the database.
     */
    @Delete
    suspend fun deleteCompletion(completion: Completion)

    /**
     * Updates an existing completion record. Useful for changing the value of a measurable habit.
     */
    @Update
    suspend fun updateCompletion(completion: Completion)

    /**
     * Gets all completions for a list of habit IDs within a specific date range.
     * This is crucial for loading data for the homepage view.
     * Returns a Flow to be reactive to database changes.
     */
    @Query("""
        SELECT * FROM completions 
        WHERE habitId IN (:habitIds) 
        AND timestamp BETWEEN :startDate AND :endDate 
        ORDER BY timestamp DESC
    """)
    fun getCompletionsForHabitsInRange(
        habitIds: List<Long>, 
        startDate: Long, 
        endDate: Long
    ): Flow<List<Completion>>

    /**
     * Gets all completions for a specific habit, ordered by most recent first.
     */
    @Query("SELECT * FROM completions WHERE habitId = :habitId ORDER BY timestamp DESC")
    fun getCompletionsForHabit(habitId: Long): Flow<List<Completion>>

    /**
     * Gets a specific completion for a habit on a specific date.
     * Returns null if no completion exists for that date.
     */
    @Query("SELECT * FROM completions WHERE habitId = :habitId AND timestamp = :timestamp")
    suspend fun getCompletionForHabitAndDate(habitId: Long, timestamp: Long): Completion?

    /**
     * Deletes all completions for a specific habit. Useful when a habit is deleted.
     */
    @Query("DELETE FROM completions WHERE habitId = :habitId")
    suspend fun deleteAllCompletionsForHabit(habitId: Long)
}