package com.example.habits9.data

import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Repository for managing habit completion data.
 * Serves as an abstraction layer between ViewModels and the CompletionDao.
 */
@Singleton
class CompletionRepository @Inject constructor(
    private val completionDao: CompletionDao
) {
    
    /**
     * Inserts a new completion record.
     */
    suspend fun insertCompletion(completion: Completion) {
        completionDao.insertCompletion(completion)
    }

    /**
     * Deletes a completion record.
     */
    suspend fun deleteCompletion(completion: Completion) {
        completionDao.deleteCompletion(completion)
    }

    /**
     * Updates an existing completion record.
     */
    suspend fun updateCompletion(completion: Completion) {
        completionDao.updateCompletion(completion)
    }

    /**
     * Gets all completions for a list of habit IDs within a specific date range.
     * Returns a Flow for reactive updates.
     */
    fun getCompletionsForHabitsInRange(
        habitIds: List<Long>, 
        startDate: Long, 
        endDate: Long
    ): Flow<List<Completion>> {
        return completionDao.getCompletionsForHabitsInRange(habitIds, startDate, endDate)
    }

    /**
     * Gets all completions for a specific habit.
     */
    fun getCompletionsForHabit(habitId: Long): Flow<List<Completion>> {
        return completionDao.getCompletionsForHabit(habitId)
    }

    /**
     * Gets a specific completion for a habit on a specific date.
     * Returns null if no completion exists.
     */
    suspend fun getCompletionForHabitAndDate(habitId: Long, timestamp: Long): Completion? {
        return completionDao.getCompletionForHabitAndDate(habitId, timestamp)
    }

    /**
     * Deletes all completions for a specific habit.
     */
    suspend fun deleteAllCompletionsForHabit(habitId: Long) {
        completionDao.deleteAllCompletionsForHabit(habitId)
    }
}