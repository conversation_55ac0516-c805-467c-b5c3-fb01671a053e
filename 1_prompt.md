## Objective: Update the Home Screen UI and Date Scroller Logic

The goal is to enhance the screen that displays the list of all habits. We will add more context to the date scroller, make the header section sticky, and refine the date range logic.

## Visual Reference

For a clear understanding of the starting point, please refer to the image named `18.jpg`, which is located in the default `image_ref` folder. This image shows the current implementation of the home screen that we will be modifying.

## Detailed Feature Implementation

### 1. Add a "Day of the Week" Row

A new row must be created to display the abbreviated day of the week. This row should be positioned directly above the existing horizontal date scroller.

**Behavior and Logic:**

- The new "Day of the Week" row must scroll horizontally in perfect sync with the date row beneath it.
- Each day's abbreviation should be vertically centered and perfectly aligned with the corresponding date number below it.
- The text for the days must be a two-letter, uppercase abbreviation (e.g., SU, MO, TU, WE, TH, FR, SA).

**Conceptual Layout:**

| SU  | MO  | TU  | WE  | ... |
| :-: | :-: | :-: | :-: | :-: |
| 14  | 15  | 16  | 17  | ... |

### 2. Implement a Sticky Header

The entire header area of the main habits screen must be "frozen" or "sticky" so it does not scroll away.

**Components of the Sticky Header:**

1.  The top-most row containing the "Habits" title and menu icons.
2.  The new "Day of the Week" row (as described in the first requirement).
3.  The "Date Number" row (the existing horizontal scroller).

**Behavior and Logic:**

- When the user scrolls the main list of habits vertically, these three rows must remain fixed at the top of the screen.
- The vertical scrolling should only affect the list of habit cards positioned below this header section.

### 3. Refine the Horizontal Date Scroller Logic

The functionality of the horizontal date scroller needs to be updated to show a specific and limited date range.

**Behavior and Logic:**

- The scroller should display a total of **15 days**: the current date ("Today") plus the 14 days immediately preceding it.
- The user must be prevented from scrolling into any future dates. The current date should be the final, right-most item in the list.
- When the screen is first opened, the scroller should automatically be positioned to show the current date.
- The user should be able to scroll horizontally from "Today" towards the left to view the dates from the previous two weeks.

---

## ⚙️ Mandatory Development Guidelines

These practices must be followed during all phases of development—planning, implementation, and review.

### 1. Refer to the Style Guide

Before starting any feature:

- Always consult the **style guide** for rules related to UI/UX, layout, naming conventions, spacing, colors, and design patterns.
- The style guide is located in the **`style.md` file in the root folder`**.
- It is the **single source of truth** for styling decisions.

### 2. Study the Reference Project

Prior to implementation:

- Review the **reference project** located in the `uhabits-dev` folder (in the root directory).
- Understand how similar features have been approached to maintain consistency and avoid duplications or contradictions.
- The reference project serves as a **blueprint** for implementation.
- This step is mandatory. **Do not proceed to implementation without this step.**

### 3. Understand the Existing Project Structure

Before writing any code:

- Spend time exploring and understanding how the current system is structured.
- Even for new features, existing components or utility functions may be reusable.
- Integrate changes **cleanly into the existing architecture** instead of creating disconnected code.

### 4. Maintain a Clean Codebase

After implementing features:

- Remove any temporary, test, or duplicate files, folders, routes, or unused components that were created during development.
- Keep the codebase **organized and clutter-free**.

### 5. Pause If There Is Any Confusion

If at any point the requirements are unclear:

- **Do not proceed** based on assumptions.
- Immediately pause and seek clarification either from the project lead or directly from me.
- It is better to get clarity than to redo or fix avoidable mistakes later.

### 6. Remove Unused Old Implementations

As part of final review:

- Identify and delete **any old, unused code** that was implemented earlier but is no longer in use.
- This includes obsolete routes, modules, features, or legacy logic.
