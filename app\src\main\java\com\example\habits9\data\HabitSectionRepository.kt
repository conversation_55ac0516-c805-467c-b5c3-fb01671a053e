package com.example.habits9.data

import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class HabitSectionRepository @Inject constructor(private val habitSectionDao: HabitSectionDao) {

    fun getAllHabitSections(): Flow<List<HabitSection>> = habitSectionDao.getAllHabitSections()

    suspend fun insertHabitSection(habitSection: HabitSection) {
        habitSectionDao.insertHabitSection(habitSection)
    }

    suspend fun updateHabitSection(habitSection: HabitSection) {
        habitSectionDao.updateHabitSection(habitSection)
    }

    suspend fun updateHabitSections(habitSections: List<HabitSection>) {
        habitSectionDao.updateHabitSections(habitSections)
    }

    suspend fun deleteHabitSection(habitSection: HabitSection) {
        habitSectionDao.deleteHabitSection(habitSection)
    }
}