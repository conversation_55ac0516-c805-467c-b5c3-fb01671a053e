## 📝 Review and Prompt Generation Instructions

Please review the entire discussion above carefully and assess whether the described features, rules, and approaches can be implemented in a more efficient or improved manner.

- If you identify any areas for optimization or refinement, please **suggest those improvements first**, and we will **discuss them** before proceeding further.
- Once the discussion concludes and everything is approved, you may proceed to **generate an effective prompt** to work on all the mentioned features.
- While generating the final prompt, ensure that you **refer to the note and strictly include the Mandatory Development Guidelines listed below as part of the generated prompt.**


## Note :

## 🧠 Communication Principles

### 1. Image-Based References
The developer **can** read or interpret images. Therefore:
- **Do** include screenshots, diagrams, or any visual references if necessary . This is the best way to communicate.
- The default location of the images folder is `image_ref` .please  **DO** include this location in all the prompts . 
- Be very clear and specific when describing UI layouts, logic, interactions, or behaviors.

### 2. Use Only `.md` Format
All prompts and documentation **must** be written in `.md` (Markdown) format:
- This is the only format the developer processes efficiently.
- **Avoid** using any other format like `.txt`, `.docx`, or image-based prompts.

### 3. Be Thoroughly Descriptive
When explaining features or changes:
- Include **every minute detail**—no assumption should be made by the developer.
- Describe flows, behaviors, expected outcomes, and interactivity explicitly.
- The goal is for the developer to understand **everything** without needing further clarification.

### 4. Avoid Direct File or Path Names
Do not refer to specific file names or routes. Instead:
- Describe files, components, or sections **based on their role or purpose** in the project.
- ✅ Example (correct):  
  *"Go to the file where the habit selection flow is implemented. This is the screen where the user selects one or more habits before proceeding to the next step."*

- ❌ Example (incorrect):  
  *"Go to the `habit_selection.xml` file."*


## ⚙️ Mandatory Development Guidelines

These practices must be followed during all phases of development—planning, implementation, and review.

### 1. Refer to the Style Guide
Before starting any feature:
- Always consult the **style guide** for rules related to UI/UX, layout, naming conventions, spacing, colors, and design patterns.
- The style guide is located in the **`style.md` file in the root folder`**.
- It is the **single source of truth** for styling decisions.

### 2. Study the Reference Project
Prior to implementation:
- Review the **reference project** located in the `uhabits-dev` folder (in the root directory).
- Understand how similar features have been approached to maintain consistency and avoid duplications or contradictions.
- The reference project serves as a **blueprint** for implementation. 
- This step is mandatory. **Do not proceed to implementation without this step.**

### 3. Understand the Existing Project Structure
Before writing any code:
- Spend time exploring and understanding how the current system is structured.
- Even for new features, existing components or utility functions may be reusable.
- Integrate changes **cleanly into the existing architecture** instead of creating disconnected code.

### 4. Maintain a Clean Codebase
After implementing features:
- Remove any temporary, test, or duplicate files, folders, routes, or unused components that were created during development.
- Keep the codebase **organized and clutter-free**.

### 5. Pause If There Is Any Confusion
If at any point the requirements are unclear:
- **Do not proceed** based on assumptions.
- Immediately pause and seek clarification either from the project lead or directly from me.
- It is better to get clarity than to redo or fix avoidable mistakes later.

### 6. Remove Unused Old Implementations
As part of final review:
- Identify and delete **any old, unused code** that was implemented earlier but is no longer in use.
- This includes obsolete routes, modules, features, or legacy logic.

---

By adhering to these principles and mandatory practices, we ensure clarity, precision, and high-quality development that scales well over time.

