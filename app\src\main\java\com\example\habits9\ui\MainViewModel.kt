package com.example.habits9.ui

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.habits9.data.Completion
import com.example.habits9.data.CompletionRepository
import com.example.habits9.data.Habit
import com.example.habits9.data.HabitRepository
import com.example.habits9.data.HabitType
import com.example.habits9.data.NumericalHabitType
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.time.temporal.TemporalAdjusters
import javax.inject.Inject

/**
 * Represents a habit with its completion status for the current week
 */
data class HabitWithCompletions(
    val habit: Habit,
    val completions: Map<Long, Boolean> = emptyMap(), // timestamp -> isCompleted
    val currentStreak: Int = 0
)

data class WeekInfo(
    val dates: List<LocalDate> = emptyList(),
    val timestamps: List<Long> = emptyList(),
    val formattedDates: List<String> = emptyList(),
    val dailyCompletionPercentages: List<Float> = emptyList(),
    val weeklyCompletionPercentage: Float = 0f,
    val weekNumber: Int = 0
)

data class MainUiState(
    val habitsWithCompletions: List<HabitWithCompletions> = emptyList(),
    val isLoading: Boolean = false,
    val currentWeekStart: Long = 0L,
    val weekInfo: WeekInfo = WeekInfo()
)

@HiltViewModel
class MainViewModel @Inject constructor(
    private val habitRepository: HabitRepository,
    private val completionRepository: CompletionRepository,
    private val userPreferencesRepository: com.example.habits9.data.UserPreferencesRepository
) : ViewModel() {

    /**
     * Calculate current streak for a habit based on completion history
     */
    private fun calculateCurrentStreak(completions: Map<Long, Boolean>, timestamps: List<Long>): Int {
        var streak = 0
        val today = LocalDate.now()
        val todayTimestamp = today.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli()
        
        // Start from today and go backwards
        var currentDate = today
        while (true) {
            val currentTimestamp = currentDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli()
            val dayStart = (currentTimestamp / (24 * 60 * 60 * 1000L)) * (24 * 60 * 60 * 1000L)
            
            if (completions[dayStart] == true) {
                streak++
                currentDate = currentDate.minusDays(1)
            } else {
                break
            }
        }
        return streak
    }

    /**
     * Calculate daily completion percentages for the week
     */
    private fun calculateDailyCompletionPercentages(
        habitsWithCompletions: List<HabitWithCompletions>,
        timestamps: List<Long>
    ): List<Float> {
        if (habitsWithCompletions.isEmpty()) return timestamps.map { 0f }
        
        return timestamps.map { timestamp ->
            val dayStart = (timestamp / (24 * 60 * 60 * 1000L)) * (24 * 60 * 60 * 1000L)
            val completedCount = habitsWithCompletions.count { habit ->
                habit.completions[dayStart] == true
            }
            completedCount.toFloat() / habitsWithCompletions.size.toFloat()
        }
    }

    /**
     * Calculate week information based on the user's first day of week preference
     * Now returns dates in reverse chronological order (today first, then yesterday, etc.)
     */
    private fun calculateWeekInfo(firstDayOfWeek: String, habitsWithCompletions: List<HabitWithCompletions> = emptyList()): WeekInfo {
        val today = LocalDate.now()
        val formatterDayOfMonth = DateTimeFormatter.ofPattern("dd")
        val formatterDayOfWeek = DateTimeFormatter.ofPattern("EE")
        
        // Determine the start day of week based on preference
        val startDayOfWeek = when (firstDayOfWeek) {
            "SUNDAY" -> DayOfWeek.SUNDAY
            "MONDAY" -> DayOfWeek.MONDAY
            else -> DayOfWeek.SUNDAY // default fallback
        }
        
        // Calculate the start of the current week
        val weekStart = today.with(TemporalAdjusters.previousOrSame(startDayOfWeek))
        
        // Generate the 7 days of the week in normal order
        val datesNormalOrder = (0..6).map { weekStart.plusDays(it.toLong()) }
        
        // Reverse the order so today appears first (rightmost)
        val dates = datesNormalOrder.reversed()
        
        // Convert to timestamps
        val timestamps = dates.map { date ->
            date.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli()
        }
        
        // Format dates for display
        val formattedDates = dates.map { date ->
            "${date.format(formatterDayOfMonth)}\n${date.format(formatterDayOfWeek).uppercase()}"
        }
        
        // Calculate daily completion percentages
        val dailyCompletionPercentages = calculateDailyCompletionPercentages(habitsWithCompletions, timestamps)
        
        // Calculate weekly completion percentage (average of daily percentages)
        val weeklyCompletionPercentage = if (dailyCompletionPercentages.isNotEmpty()) {
            dailyCompletionPercentages.average().toFloat()
        } else {
            0f
        }
        
        // Calculate week number
        val weekNumber = today.get(java.time.temporal.WeekFields.of(java.util.Locale.getDefault()).weekOfYear())
        
        return WeekInfo(
            dates = dates,
            timestamps = timestamps,
            formattedDates = formattedDates,
            dailyCompletionPercentages = dailyCompletionPercentages,
            weeklyCompletionPercentage = weeklyCompletionPercentage,
            weekNumber = weekNumber
        )
    }
    
    private fun getCurrentWeekStart(firstDayOfWeek: String): Long {
        return calculateWeekInfo(firstDayOfWeek).timestamps.last() // Last because dates are in reverse order
    }

    private fun getCurrentWeekEnd(firstDayOfWeek: String): Long {
        val weekStart = getCurrentWeekStart(firstDayOfWeek)
        return weekStart + (7 * 24 * 60 * 60 * 1000L) - 1 // 7 days minus 1ms
    }

    // Combine habits with their completion data for the current week, respecting first day of week preference
    val uiState: StateFlow<MainUiState> =
        combine(
            habitRepository.getAllHabits(),
            userPreferencesRepository.firstDayOfWeek
        ) { habits, firstDayOfWeek ->
            val weekInfo = calculateWeekInfo(firstDayOfWeek)
            if (habits.isEmpty()) {
                MainUiState(
                    isLoading = false, 
                    currentWeekStart = getCurrentWeekStart(firstDayOfWeek),
                    weekInfo = weekInfo
                )
            } else {
                val habitIds = habits.map { it.id }
                // For now, create habits without completion data - we'll load completions separately
                val habitsWithCompletions = habits.map { habit ->
                    HabitWithCompletions(habit, emptyMap())
                }
                MainUiState(
                    habitsWithCompletions = habitsWithCompletions,
                    isLoading = false,
                    currentWeekStart = getCurrentWeekStart(firstDayOfWeek),
                    weekInfo = weekInfo
                )
            }
        }.stateIn(
            scope = viewModelScope,
            started = kotlinx.coroutines.flow.SharingStarted.WhileSubscribed(5000),
            initialValue = MainUiState(
                isLoading = true, 
                currentWeekStart = getCurrentWeekStart("SUNDAY"),
                weekInfo = calculateWeekInfo("SUNDAY")
            )
        )

    // Load completions separately and update UI state
    private val _completionsState = MutableStateFlow<Map<Long, Map<Long, Boolean>>>(emptyMap())
    
    init {
        // Load completions when habits or first day of week preference changes
        viewModelScope.launch {
            combine(
                habitRepository.getAllHabits(),
                userPreferencesRepository.firstDayOfWeek
            ) { habits, firstDayOfWeek ->
                Pair(habits, firstDayOfWeek)
            }.collect { (habits, firstDayOfWeek) ->
                if (habits.isNotEmpty()) {
                    val habitIds = habits.map { it.id }
                    // Calculate the date range for the current week based on firstDayOfWeek
                    val weekStart = getCurrentWeekStart(firstDayOfWeek)
                    val weekEnd = getCurrentWeekEnd(firstDayOfWeek)
                    
                    // Launch a new coroutine for each date range change to avoid nested collect
                    viewModelScope.launch {
                        completionRepository.getCompletionsForHabitsInRange(
                            habitIds = habitIds,
                            startDate = weekStart,
                            endDate = weekEnd
                        ).collect { completions ->
                            val completionsMap = completions.groupBy { it.habitId }
                                .mapValues { (_, habitCompletions) ->
                                    habitCompletions.associate { completion ->
                                        completion.timestamp to true
                                    }
                                }
                            _completionsState.value = completionsMap
                        }
                    }
                } else {
                    // Clear completions when no habits exist
                    _completionsState.value = emptyMap()
                }
            }
        }
    }

    // Enhanced UI state that includes completion data
    val enhancedUiState: StateFlow<MainUiState> =
        combine(uiState, _completionsState, userPreferencesRepository.firstDayOfWeek) { baseState, completionsMap, firstDayOfWeek ->
            if (baseState.habitsWithCompletions.isEmpty()) {
                baseState.copy(weekInfo = calculateWeekInfo(firstDayOfWeek, emptyList()))
            } else {
                val enhancedHabits = baseState.habitsWithCompletions.map { habitWithCompletions ->
                    val completions = completionsMap[habitWithCompletions.habit.id] ?: emptyMap()
                    val streak = calculateCurrentStreak(completions, baseState.weekInfo.timestamps)
                    habitWithCompletions.copy(
                        completions = completions,
                        currentStreak = streak
                    )
                }
                val updatedWeekInfo = calculateWeekInfo(firstDayOfWeek, enhancedHabits)
                baseState.copy(
                    habitsWithCompletions = enhancedHabits,
                    weekInfo = updatedWeekInfo
                )
            }
        }.stateIn(
            scope = viewModelScope,
            started = kotlinx.coroutines.flow.SharingStarted.WhileSubscribed(5000),
            initialValue = MainUiState(
                isLoading = true, 
                currentWeekStart = getCurrentWeekStart("SUNDAY"),
                weekInfo = calculateWeekInfo("SUNDAY")
            )
        )


    /**
     * Toggles the completion status of a Yes/No habit for a specific date.
     * If a completion exists, it will be deleted. If not, a new completion will be created.
     */
    fun toggleCompletion(habitId: Long, date: Long) {
        viewModelScope.launch {
            try {
                // Normalize the date to start of day using the same approach as reference project
                // This ensures we get the exact start of the day without timezone issues
                val dayLength = 24 * 60 * 60 * 1000L // milliseconds in a day
                val dayStart = (date / dayLength) * dayLength
                
                // Check if completion already exists for this habit and date
                val existingCompletion = completionRepository.getCompletionForHabitAndDate(habitId, dayStart)
                
                if (existingCompletion != null) {
                    // Completion exists, delete it
                    completionRepository.deleteCompletion(existingCompletion)
                } else {
                    // No completion exists, create a new one
                    val newCompletion = Completion(
                        habitId = habitId,
                        timestamp = dayStart,
                        value = null // null for Yes/No habits
                    )
                    completionRepository.insertCompletion(newCompletion)
                }
            } catch (e: Exception) {
                // Handle error - could log or show error message
                e.printStackTrace()
            }
        }
    }
}