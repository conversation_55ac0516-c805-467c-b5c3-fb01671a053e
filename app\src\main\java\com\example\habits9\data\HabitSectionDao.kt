package com.example.habits9.data

import androidx.room.*
import kotlinx.coroutines.flow.Flow

@Dao
interface HabitSectionDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertHabitSection(habitSection: HabitSection)

    @Update
    suspend fun updateHabitSection(habitSection: HabitSection)

    @Update
    suspend fun updateHabitSections(habitSections: List<HabitSection>)

    @Delete
    suspend fun deleteHabitSection(habitSection: HabitSection)

    @Query("SELECT * FROM habit_sections ORDER BY displayOrder ASC")
    fun getAllHabitSections(): Flow<List<HabitSection>>
}